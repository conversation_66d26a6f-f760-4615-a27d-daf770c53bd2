#!/usr/bin/env node

/**
 * Test script to verify the catch-all alias fix
 * This script tests the POST /api/webhooks/alias endpoint with catch-all aliases
 */

const API_BASE = 'http://localhost:3000';
const API_KEY = 'ak_87eb37fc6f9d99185aab490ff909dd20d72b92c17cd3916c7a1dd258c244aff8';

async function makeRequest(method, endpoint, body = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'X-API-KEY': API_KEY,
      'Content-Type': 'application/json'
    }
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  console.log(`\n🔄 ${method} ${endpoint}`);
  if (body) {
    console.log('📤 Request body:', JSON.stringify(body, null, 2));
  }

  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    console.log(`📥 Response (${response.status}):`, JSON.stringify(data, null, 2));
    return { status: response.status, data };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { error: error.message };
  }
}

async function testCatchAllFix() {
  console.log('🧪 Testing Catch-all Alias Fix');
  console.log('================================');

  // Step 1: Get domains to find a domain ID
  console.log('\n📋 Step 1: Get domains');
  const domainsResponse = await makeRequest('GET', '/api/domains');
  
  if (domainsResponse.status !== 200 || !domainsResponse.data.domains?.length) {
    console.error('❌ No domains found. Please create a domain first.');
    return;
  }

  const domainId = domainsResponse.data.domains[0].id;
  const domainName = domainsResponse.data.domains[0].domain;
  console.log(`✅ Using domain: ${domainName} (${domainId})`);

  // Step 2: Try to create catch-all alias (first time - should succeed)
  console.log('\n📋 Step 2: Create catch-all alias (first time)');
  const catchAllData = {
    domainId: domainId,
    webhookUrl: 'https://test.example.com/webhook1',
    webhookName: 'Test Catch-all Webhook 1',
    webhookDescription: 'First catch-all webhook test',
    aliasType: 'catchall',
    syncWithDomain: true,
    autoVerify: true
  };

  const firstResponse = await makeRequest('POST', '/api/webhooks/alias', catchAllData);
  
  if (firstResponse.status === 201) {
    console.log('✅ First catch-all creation succeeded');
  } else {
    console.log('⚠️ First catch-all creation failed (might already exist)');
  }

  // Step 3: Try to create catch-all alias again (should update existing)
  console.log('\n📋 Step 3: Create catch-all alias (second time - should update)');
  const updatedCatchAllData = {
    domainId: domainId,
    webhookUrl: 'https://test.example.com/webhook2',
    webhookName: 'Test Catch-all Webhook 2 (Updated)',
    webhookDescription: 'Updated catch-all webhook test',
    aliasType: 'catchall',
    syncWithDomain: true,
    autoVerify: true
  };

  const secondResponse = await makeRequest('POST', '/api/webhooks/alias', updatedCatchAllData);
  
  if (secondResponse.status === 201) {
    console.log('✅ Catch-all update succeeded!');
    console.log('🎉 Fix is working - existing catch-all was updated instead of failing');
  } else if (secondResponse.status === 409) {
    console.log('❌ Fix not working - still getting 409 conflict error');
  } else {
    console.log(`⚠️ Unexpected response: ${secondResponse.status}`);
  }

  // Step 4: Verify the catch-all alias exists and has the updated webhook
  console.log('\n📋 Step 4: Verify catch-all alias');
  const aliasesResponse = await makeRequest('GET', `/api/aliases?domainId=${domainId}`);
  
  if (aliasesResponse.status === 200) {
    const catchAllAlias = aliasesResponse.data.aliases?.find(alias => alias.email.startsWith('*@'));
    if (catchAllAlias) {
      console.log('✅ Catch-all alias found:', {
        email: catchAllAlias.email,
        webhookId: catchAllAlias.webhookId,
        active: catchAllAlias.active
      });
    } else {
      console.log('❌ Catch-all alias not found');
    }
  }

  console.log('\n🏁 Test completed');
}

// Run the test
testCatchAllFix().catch(console.error);
