#!/usr/bin/env node

/**
 * Test script to verify the webhook-alias linking fix
 * This script tests webhook URL updates and ensures aliases stay linked
 */

const API_BASE = 'http://localhost:3000';
const API_KEY = 'ak_87eb37fc6f9d99185aab490ff909dd20d72b92c17cd3916c7a1dd258c244aff8';

async function makeRequest(method, endpoint, body = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'X-API-KEY': API_KEY,
      'Content-Type': 'application/json'
    }
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  console.log(`\n🔄 ${method} ${endpoint}`);
  if (body) {
    console.log('📤 Request body:', JSON.stringify(body, null, 2));
  }

  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    console.log(`📥 Response (${response.status}):`, JSON.stringify(data, null, 2));
    return { status: response.status, data };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { error: error.message };
  }
}

async function testWebhookLinkingFix() {
  console.log('🧪 Testing Webhook-Alias Linking Fix');
  console.log('====================================');

  // Step 1: Get domains to find a domain ID
  console.log('\n📋 Step 1: Get domains');
  const domainsResponse = await makeRequest('GET', '/api/domains');
  
  if (domainsResponse.status !== 200 || !domainsResponse.data.domains?.length) {
    console.error('❌ No domains found. Please create a domain first.');
    return;
  }

  const domainId = domainsResponse.data.domains[0].id;
  const domainName = domainsResponse.data.domains[0].domain;
  console.log(`✅ Using domain: ${domainName} (${domainId})`);

  // Step 2: Create a test webhook
  console.log('\n📋 Step 2: Create test webhook');
  const webhookData = {
    name: 'Test Webhook Linking Fix',
    url: 'https://test.example.com/webhook-original',
    description: 'Test webhook for linking fix verification'
  };

  const webhookResponse = await makeRequest('POST', '/api/webhooks', webhookData);
  
  if (webhookResponse.status !== 201) {
    console.error('❌ Failed to create test webhook');
    return;
  }

  const webhookId = webhookResponse.data.webhook.id;
  console.log(`✅ Created test webhook: ${webhookId}`);

  // Step 3: Get aliases for the domain
  console.log('\n📋 Step 3: Get aliases for domain');
  const aliasesResponse = await makeRequest('GET', `/api/aliases?domainId=${domainId}`);
  
  if (aliasesResponse.status !== 200 || !aliasesResponse.data.aliases?.length) {
    console.error('❌ No aliases found for domain');
    return;
  }

  // Find a non-catch-all alias to test with
  const testAlias = aliasesResponse.data.aliases.find(alias => !alias.email.startsWith('*@'));
  if (!testAlias) {
    console.error('❌ No non-catch-all alias found for testing');
    return;
  }

  const aliasId = testAlias.id;
  const aliasEmail = testAlias.email;
  console.log(`✅ Using alias: ${aliasEmail} (${aliasId})`);

  // Step 4: Link the webhook to the alias
  console.log('\n📋 Step 4: Link webhook to alias');
  const linkResponse = await makeRequest('PUT', `/api/aliases/${aliasId}/webhook`, {
    webhookId: webhookId
  });

  if (linkResponse.status !== 200) {
    console.error('❌ Failed to link webhook to alias');
    return;
  }

  console.log('✅ Successfully linked webhook to alias');

  // Step 5: Verify the linkage
  console.log('\n📋 Step 5: Verify initial linkage');
  const aliasCheckResponse = await makeRequest('GET', `/api/aliases/${aliasId}`);
  
  if (aliasCheckResponse.status === 200) {
    const currentWebhookId = aliasCheckResponse.data.webhook?.id;
    if (currentWebhookId === webhookId) {
      console.log('✅ Initial linkage verified correctly');
    } else {
      console.log(`❌ Initial linkage incorrect: expected ${webhookId}, got ${currentWebhookId}`);
    }
  }

  // Step 6: Update the webhook URL (simulating test/production switch)
  console.log('\n📋 Step 6: Update webhook URL (simulating test/production switch)');
  const updateResponse = await makeRequest('PUT', `/api/webhooks/${webhookId}`, {
    url: 'https://test.example.com/webhook-updated',
    description: 'Updated webhook URL for linking test'
  });

  if (updateResponse.status !== 200) {
    console.error('❌ Failed to update webhook URL');
    return;
  }

  console.log('✅ Successfully updated webhook URL');

  // Step 7: Verify the linkage is still intact after URL update
  console.log('\n📋 Step 7: Verify linkage after URL update');
  const finalAliasCheckResponse = await makeRequest('GET', `/api/aliases/${aliasId}`);
  
  if (finalAliasCheckResponse.status === 200) {
    const finalWebhookId = finalAliasCheckResponse.data.webhook?.id;
    const finalWebhookUrl = finalAliasCheckResponse.data.webhook?.url;
    
    if (finalWebhookId === webhookId) {
      console.log('✅ Webhook-alias linkage maintained after URL update!');
      console.log(`✅ Webhook URL correctly updated to: ${finalWebhookUrl}`);
      console.log('🎉 Fix is working - webhook stays linked to alias during URL updates');
    } else {
      console.log(`❌ Linkage broken after URL update: expected ${webhookId}, got ${finalWebhookId}`);
      console.log('❌ Fix not working - webhook became orphaned');
    }
  }

  // Step 8: Cleanup - delete the test webhook
  console.log('\n📋 Step 8: Cleanup test webhook');
  
  // First detach from alias
  await makeRequest('PUT', `/api/aliases/${aliasId}/webhook`, {
    webhookId: null
  });
  
  // Then delete the webhook
  const deleteResponse = await makeRequest('DELETE', `/api/webhooks/${webhookId}`);
  
  if (deleteResponse.status === 200) {
    console.log('✅ Test webhook cleaned up successfully');
  } else {
    console.log('⚠️ Failed to cleanup test webhook (may need manual cleanup)');
  }

  console.log('\n🏁 Test completed');
}

// Run the test
testWebhookLinkingFix().catch(console.error);
